Release
- set the flag if a migration is necessary or not (MigrationProcessing)
- update readme / changelog
- update version in pyproject.toml
- push to github -> check pipeline
- merge feature branch
- create tag in format vx.x.x
- push everything
- create release

Version 0.2.2
-------------
[x] catch problems when reading zip files
[] all files need to be prefixed with the adsh number
[] migration check to reset/housekeep based on version.. 


Version 0.2.1
-------------
[x] fix order of parameters for logging in housekeeping
[x] fix mixed up pre and num file name during sec-style formatting
[x] removed helper column fy_real from final sub_df

Version 0.2.0
-------------
01. [x] version in code
    [x] update check

02. [x] build quarter zip files from daily zip filenames
      [x] for all quarters
      [x] starting from a specific quarter

03. [x] clean up for quarter
      [x] cleanup files for a specific quarter
      [x] cleanup db tables for a specific quarter
      [x] option to cleanup daily / quarter zip files as well

04. [x] introduce a configuration class for secdaily
      [x] define user agent
      [x] define workdir and subdirs
      [x] define intermediate files cleaning
      [x] define final files cleaning

05. [x] code/module documentation

06. [x] Radme/Change log anpassen



Version 0.3.0
-------------

xx. [] es wäre gut, dass beim erstellen des mass-quarter-tests auch gleich eine Datei mit den unequal Einträgen erstellt wird,
       damit die Analyse danach einfacher ist

xx. [] Verbessern der Ergebnisse, falls möglich und nötig

xx. [] single file processing
      [] strip html file
      [] analyse html file and available xml-file -> where is the data coming from to create pre?

xx. [] mehr end-to-end testfälle

Version 0.4.0
-------------
xx. [] segments hinzüfügen


History
=======

Version 0.1.0
-------------
01. [x] sponsoring message and end end beginning
02. [x] adapt readme / add limitations
03. [x] end to end tests -> kann da der agent helfen? und zwar so, dass er reproduzierbar neue beispiele  hinzufügen kann?
      [x] apple 10k
      [x] 0001477932-24-008123 (mit Einträgen on plabel)
04. [x] reference to secfsdstools
 
 Version 0.0.1
 -------------
 1. [ok] subfolder in src "secdaily" mit allem code
 2. [ok] pyproject toml entsprechend setzen (packages definition)
 3. [ok] test folder nach tests umbenennen
 4. [ok] tests_ext nach sandbox umbenennen
 5. [ok] .vscode/settings.json für tests korrekt konfigurieren
     {
         "python.testing.pytestArgs": [
             "tests", "sandbox"
         ],
         "python.testing.unittestEnabled": false,
         "python.testing.pytestEnabled": true
     }
 7. [ok] definieren einer neuen conda umgebung
 8. [ok] definieren der notwendigen dependencies im pyproject.toml, bis ausführung läuft
 9. [ok] sicherstellen, dass alle normlan tests wieder laufen
10. [ok] Optimierung failed downloads
       [ok] manage process index -> default values
       [ok] copy to processing -> correct übernehmen
       [ok] selection for missing download -> nicht übernehmen, falls url empty
11. [ok] implement "to csv" logic für label daten
       -> achtung, ist text -> muss spezielle delimiter verwenden.
          oder dann mit quotes arbeiten
12. [ok] neuen step hinzufügen, der aus den raw csv files die finallen SEC-Formatted style macht
      [ok] rewrite parsint lab, so that key contains tag/version/laybeltype
      [ok] create map from lab infos to lookup plabel for pre 
      [ok] labels are stripped of single quotes, maybe also other content
14. [ok] einzelner PRE und NUM Test mit soll / ist vergleich 
   - [ok] plabel sollte gleich sein
     - [ok] download label files
     - [ok] prozess label files
15. [ok] es wird alles noch mit index geschrieben .. 
16. [ok] Mass Test für Q4/2024 
      [ok] DB setup / dataclass und write logik schreiben
      [ok] db daten für einzelnes adsh bereitstellen und in db speichern
      [ok] logik um gesammtes quartel zu vergleichen und zu loggen
      [ok] Auswerten einzelner reports ermöglichen
      [ok] pre vergleich -> ohne line und report
      [ok] auswerten des quartals -> MassTestTable neu anlegen
      [ok] Übersichts report erstellen und ablegen

17. [ok] verbesserte Ablageverzeichnisstruktur für alle Schritte
       _1_xml/<qrtr>/<filedate>/..
       _2_csv/<qrtr>/<filedate>/..
       _3_secstyle/<qrtr>/<filedate>/..
       _4_zip/<qrtr>
       
      [ok] Superklasse für processing steps mit basis Verzeichnissen und errorlogging
      [ok] Ablageverzeichnisstruktur anpassen

18. [ok] error handling:
      [ok] warning: 
          C:\ieu\projects\sec_processing\src\secdaily\_03_secstyle\formatting\SECPreNumFormatting.py:73: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a 
          future version of pandas. Value '' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.
          df.loc[df.segments.isnull(), 'segments'] = ""
      [ok] write normal catches to error log as well

19. [ok] familarize with ruff -> which commands can help to check whole code and directly fix most common lint problems?

19.  [ok] create final quarter zip file containing all daily
      [ok] find quarters which have entries that are not part of the zip -> in sqldb tool?
      [ok] find all entries for the found quarters
      [ok] create content for sub_txt file
      [ok] create daily zip files
      [ok] ensure path of zipfiles is full path

20. [ok] activate project
      [ok] fix linting problems
      [ok] ensure existing tests run.
      [ok] activate build pipeline with linting and tests for py3.10/3.11/3.12/3.13
      [ok] project.toml, including title of library
      
21. [ok] activate deployment to pypi
      [ok] create official 0.0.1 release

