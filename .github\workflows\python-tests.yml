name: Python Tests

on:
  push:  # Run on any push to any branch
  pull_request:  # Still keep PR trigger for visibility in PR interface

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]
      fail-fast: false  # Continue with other versions even if one fails

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 2.1.1
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}-v2

    - name: Install dependencies
      run: poetry install --with dev

    - name: <PERSON><PERSON> with pylint
      run: |
        poetry run pylint src tests

    - name: Run tests with pytest
      run: |
        poetry run pytest tests -v


