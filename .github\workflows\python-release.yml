name: Release to PyPI

on:
  release:
    types: [published]  # Triggers when a release is published

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      id-token: write  # Required for trusted publishing to PyPI

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.10
      uses: actions/setup-python@v5
      with:
        python-version: "3.10"

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 2.1.1
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Build package
      run: poetry build

    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
