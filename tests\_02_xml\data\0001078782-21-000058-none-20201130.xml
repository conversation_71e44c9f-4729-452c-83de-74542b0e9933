<?xml version='1.0' encoding='iso-8859-1'?>
<!-- Produced by edgar-services.com using EDGARsuite software, Advanced Computer Innovations, Inc., Copyright (C) 2008-2021 [PPXV9M0ULJ50VFA93YWW]. www.edgarsuite.com -->
<xbrl xmlns:nonnum='http://www.xbrl.org/dtr/type/non-numeric' xmlns='http://www.xbrl.org/2003/instance' xmlns:us-gaap='http://fasb.org/us-gaap/2020-01-31' xmlns:xbrldi='http://xbrl.org/2006/xbrldi' xmlns:dei='http://xbrl.sec.gov/dei/2020-01-31' xmlns:fil='http://www.threeam.com/20201130' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:utr='http://www.xbrl.org/2009/utr' xmlns:link='http://www.xbrl.org/2003/linkbase' xmlns:iso4217='http://www.xbrl.org/2003/iso4217' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:xbrli='http://www.xbrl.org/2003/instance'>
	<link:schemaRef xlink:type='simple' xlink:href='none-20201130.xsd' />
	<dei:EntityRegistrantName contextRef='D200601_201130'>3AM TECHNOLOGIES INC</dei:EntityRegistrantName>
	<dei:EntityCentralIndexKey contextRef='D200601_201130'>0001667615</dei:EntityCentralIndexKey>
	<dei:CurrentFiscalYearEndDate contextRef='D200601_201130'>--05-31</dei:CurrentFiscalYearEndDate>
	<dei:DocumentType contextRef='D200601_201130'>10-Q</dei:DocumentType>
	<dei:DocumentQuarterlyReport contextRef='D200601_201130'>true</dei:DocumentQuarterlyReport>
	<dei:DocumentPeriodEndDate contextRef='D200601_201130'>2020-11-30</dei:DocumentPeriodEndDate>
	<dei:DocumentTransitionReport contextRef='D200601_201130'>false</dei:DocumentTransitionReport>
	<dei:EntityFileNumber contextRef='D200601_201130'>**********</dei:EntityFileNumber>
	<dei:EntityTaxIdentificationNumber contextRef='D200601_201130'>35-2553515</dei:EntityTaxIdentificationNumber>
	<dei:EntityAddressAddressLine1 contextRef='D200601_201130'>45B West Wilmot Street, Unit 1.</dei:EntityAddressAddressLine1>
	<dei:EntityAddressCityOrTown contextRef='D200601_201130'>Richmond Hill, Ontario</dei:EntityAddressCityOrTown>
	<dei:EntityAddressCountry contextRef='D200601_201130'>CA</dei:EntityAddressCountry>
	<dei:EntityAddressPostalZipCode contextRef='D200601_201130'>L4B 2P3</dei:EntityAddressPostalZipCode>
	<dei:EntityAddressAddressDescription contextRef='D200601_201130'>Address of principal executive offices</dei:EntityAddressAddressDescription>
	<dei:CityAreaCode contextRef='D200601_201130'>204</dei:CityAreaCode>
	<dei:LocalPhoneNumber contextRef='D200601_201130'>666-2981</dei:LocalPhoneNumber>
	<dei:PhoneFaxNumberDescription contextRef='D200601_201130'>Registrant&amp;#146;s telephone number, including area code</dei:PhoneFaxNumberDescription>
	<dei:EntityCurrentReportingStatus contextRef='D200601_201130'>No</dei:EntityCurrentReportingStatus>
	<dei:EntityInteractiveDataCurrent contextRef='D200601_201130'>No</dei:EntityInteractiveDataCurrent>
	<dei:EntityFilerCategory contextRef='D200601_201130'>Non-accelerated Filer</dei:EntityFilerCategory>
	<dei:EntitySmallBusiness contextRef='D200601_201130'>true</dei:EntitySmallBusiness>
	<dei:EntityEmergingGrowthCompany contextRef='D200601_201130'>true</dei:EntityEmergingGrowthCompany>
	<dei:EntityExTransitionPeriod contextRef='D200601_201130'>true</dei:EntityExTransitionPeriod>
	<dei:EntityShellCompany contextRef='D200601_201130'>true</dei:EntityShellCompany>
	<dei:EntityListingParValuePerShare decimals='INF' contextRef='D201020' unitRef='UsdPerShare'>0.001</dei:EntityListingParValuePerShare>
	<dei:EntityCommonStockSharesOutstanding decimals='INF' contextRef='I200115' unitRef='Shares'>7500000</dei:EntityCommonStockSharesOutstanding>
	<dei:AmendmentFlag contextRef='D200601_201130'>false</dei:AmendmentFlag>
	<dei:DocumentFiscalYearFocus contextRef='D200601_201130'>2021</dei:DocumentFiscalYearFocus>
	<dei:DocumentFiscalPeriodFocus contextRef='D200601_201130'>Q2</dei:DocumentFiscalPeriodFocus>
	<us-gaap:AssetsCurrent decimals='INF' contextRef='I201130' unitRef='USD'>1364</us-gaap:AssetsCurrent>
	<us-gaap:AssetsCurrent decimals='INF' contextRef='I200531' unitRef='USD'>1434</us-gaap:AssetsCurrent>
	<us-gaap:AccountsPayableAndAccruedLiabilitiesCurrent decimals='INF' contextRef='I201130' unitRef='USD'>34763</us-gaap:AccountsPayableAndAccruedLiabilitiesCurrent>
	<us-gaap:AccountsPayableAndAccruedLiabilitiesCurrent decimals='INF' contextRef='I200531' unitRef='USD'>31763</us-gaap:AccountsPayableAndAccruedLiabilitiesCurrent>
	<us-gaap:DueToRelatedPartiesCurrent decimals='INF' contextRef='I201130' unitRef='USD'>50127</us-gaap:DueToRelatedPartiesCurrent>
	<us-gaap:DueToRelatedPartiesCurrent decimals='INF' contextRef='I200531' unitRef='USD'>37512</us-gaap:DueToRelatedPartiesCurrent>
	<us-gaap:LiabilitiesCurrent decimals='INF' contextRef='I201130' unitRef='USD'>84890</us-gaap:LiabilitiesCurrent>
	<us-gaap:LiabilitiesCurrent decimals='INF' contextRef='I200531' unitRef='USD'>69275</us-gaap:LiabilitiesCurrent>
	<us-gaap:PreferredStockSharesAuthorized decimals='INF' contextRef='I201130' unitRef='Shares'>********</us-gaap:PreferredStockSharesAuthorized>
	<us-gaap:PreferredStockSharesAuthorized decimals='INF' contextRef='I200531' unitRef='Shares'>********</us-gaap:PreferredStockSharesAuthorized>
	<us-gaap:PreferredStockParOrStatedValuePerShare decimals='INF' contextRef='I201130' unitRef='UsdPerShare'>0.001</us-gaap:PreferredStockParOrStatedValuePerShare>
	<us-gaap:PreferredStockParOrStatedValuePerShare decimals='INF' contextRef='I200531' unitRef='UsdPerShare'>0.001</us-gaap:PreferredStockParOrStatedValuePerShare>
	<us-gaap:PreferredStockSharesIssued decimals='INF' contextRef='I201130' unitRef='Shares'>0</us-gaap:PreferredStockSharesIssued>
	<us-gaap:PreferredStockSharesIssued decimals='INF' contextRef='I200531' unitRef='Shares'>0</us-gaap:PreferredStockSharesIssued>
	<us-gaap:PreferredStockSharesOutstanding decimals='INF' contextRef='I201130' unitRef='Shares'>0</us-gaap:PreferredStockSharesOutstanding>
	<us-gaap:PreferredStockSharesOutstanding decimals='INF' contextRef='I200531' unitRef='Shares'>0</us-gaap:PreferredStockSharesOutstanding>
	<us-gaap:PreferredStockValue decimals='128' contextRef='I201130' unitRef='USD'>0</us-gaap:PreferredStockValue>
	<us-gaap:PreferredStockValue decimals='128' contextRef='I200531' unitRef='USD'>0</us-gaap:PreferredStockValue>
	<us-gaap:CommonStockSharesAuthorized decimals='INF' contextRef='I201130' unitRef='Shares'>20000000</us-gaap:CommonStockSharesAuthorized>
	<us-gaap:CommonStockSharesAuthorized decimals='INF' contextRef='I200531' unitRef='Shares'>20000000</us-gaap:CommonStockSharesAuthorized>
	<us-gaap:CommonStockParOrStatedValuePerShare decimals='INF' contextRef='I201130' unitRef='UsdPerShare'>0.001</us-gaap:CommonStockParOrStatedValuePerShare>
	<us-gaap:CommonStockParOrStatedValuePerShare decimals='INF' contextRef='I200531' unitRef='UsdPerShare'>0.001</us-gaap:CommonStockParOrStatedValuePerShare>
	<us-gaap:CommonStockSharesIssued decimals='INF' contextRef='I201130' unitRef='Shares'>7500000</us-gaap:CommonStockSharesIssued>
	<us-gaap:CommonStockSharesOutstanding decimals='INF' contextRef='I201130' unitRef='Shares'>7500000</us-gaap:CommonStockSharesOutstanding>
	<us-gaap:CommonStockSharesIssued decimals='INF' contextRef='I200531' unitRef='Shares'>7500000</us-gaap:CommonStockSharesIssued>
	<us-gaap:CommonStockSharesOutstanding decimals='INF' contextRef='I200531' unitRef='Shares'>7500000</us-gaap:CommonStockSharesOutstanding>
	<us-gaap:CommonStockValue decimals='INF' contextRef='I201130' unitRef='USD'>7500</us-gaap:CommonStockValue>
	<us-gaap:CommonStockValue decimals='INF' contextRef='I200531' unitRef='USD'>7500</us-gaap:CommonStockValue>
	<us-gaap:AdditionalPaidInCapital decimals='INF' contextRef='I201130' unitRef='USD'>67500</us-gaap:AdditionalPaidInCapital>
	<us-gaap:AdditionalPaidInCapital decimals='INF' contextRef='I200531' unitRef='USD'>67500</us-gaap:AdditionalPaidInCapital>
	<us-gaap:RetainedEarningsAccumulatedDeficit decimals='INF' contextRef='I200531' unitRef='USD'>-142841</us-gaap:RetainedEarningsAccumulatedDeficit>
	<us-gaap:StockholdersEquity decimals='INF' contextRef='I201130' unitRef='USD'>-83526</us-gaap:StockholdersEquity>
	<us-gaap:StockholdersEquity decimals='INF' contextRef='I200531' unitRef='USD'>-67841</us-gaap:StockholdersEquity>
	<us-gaap:LiabilitiesAndStockholdersEquity decimals='INF' contextRef='I201130' unitRef='USD'>1364</us-gaap:LiabilitiesAndStockholdersEquity>
	<us-gaap:LiabilitiesAndStockholdersEquity decimals='INF' contextRef='I200531' unitRef='USD'>1434</us-gaap:LiabilitiesAndStockholdersEquity>
	<us-gaap:GeneralAndAdministrativeExpense decimals='INF' contextRef='D200901_201130' unitRef='USD'>6420</us-gaap:GeneralAndAdministrativeExpense>
	<us-gaap:GeneralAndAdministrativeExpense decimals='INF' contextRef='D190901_191130' unitRef='USD'>4910</us-gaap:GeneralAndAdministrativeExpense>
	<us-gaap:GeneralAndAdministrativeExpense decimals='INF' contextRef='D200601_201130' unitRef='USD'>15685</us-gaap:GeneralAndAdministrativeExpense>
	<us-gaap:GeneralAndAdministrativeExpense decimals='INF' contextRef='D190601_191130' unitRef='USD'>10943</us-gaap:GeneralAndAdministrativeExpense>
	<us-gaap:OperatingExpenses decimals='INF' contextRef='D200901_201130' unitRef='USD'>6420</us-gaap:OperatingExpenses>
	<us-gaap:OperatingExpenses decimals='INF' contextRef='D190901_191130' unitRef='USD'>4910</us-gaap:OperatingExpenses>
	<us-gaap:OperatingExpenses decimals='INF' contextRef='D200601_201130' unitRef='USD'>15685</us-gaap:OperatingExpenses>
	<us-gaap:OperatingExpenses decimals='INF' contextRef='D190601_191130' unitRef='USD'>10943</us-gaap:OperatingExpenses>
	<us-gaap:NetIncomeLoss decimals='INF' contextRef='D200901_201130' unitRef='USD'>-6420</us-gaap:NetIncomeLoss>
	<us-gaap:NetIncomeLoss decimals='INF' contextRef='D190901_191130' unitRef='USD'>-4910</us-gaap:NetIncomeLoss>
	<us-gaap:NetIncomeLoss decimals='INF' contextRef='D200601_201130' unitRef='USD'>-15685</us-gaap:NetIncomeLoss>
	<us-gaap:NetIncomeLoss decimals='INF' contextRef='D190601_191130' unitRef='USD'>-10943</us-gaap:NetIncomeLoss>
	<us-gaap:EarningsPerShareBasicAndDiluted decimals='INF' contextRef='D200901_201130' unitRef='UsdPerShare'>0.00</us-gaap:EarningsPerShareBasicAndDiluted>
	<us-gaap:EarningsPerShareBasicAndDiluted decimals='INF' contextRef='D190901_191130' unitRef='UsdPerShare'>0.00</us-gaap:EarningsPerShareBasicAndDiluted>
	<us-gaap:EarningsPerShareBasicAndDiluted decimals='INF' contextRef='D200601_201130' unitRef='UsdPerShare'>0.00</us-gaap:EarningsPerShareBasicAndDiluted>
	<us-gaap:EarningsPerShareBasicAndDiluted decimals='INF' contextRef='D190601_191130' unitRef='UsdPerShare'>0.00</us-gaap:EarningsPerShareBasicAndDiluted>
	<us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted decimals='INF' contextRef='D200901_201130' unitRef='Shares'>7500000</us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted>
	<us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted decimals='INF' contextRef='D190901_191130' unitRef='Shares'>7500000</us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted>
	<us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted decimals='INF' contextRef='D200601_201130' unitRef='Shares'>7500000</us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted>
	<us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted decimals='INF' contextRef='D190601_191130' unitRef='Shares'>7500000</us-gaap:WeightedAverageNumberOfShareOutstandingBasicAndDiluted>
	<us-gaap:SharesOutstanding decimals='INF' contextRef='I200531_StEqComps-CommonStock' unitRef='Shares'>7500000</us-gaap:SharesOutstanding>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200531_StEqComps-CommonStock' unitRef='USD'>7500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200531_StEqComps-AddPaidInCap' unitRef='USD'>67500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200531_StEqComps-RetainedEarnings' unitRef='USD'>-142841</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200531' unitRef='USD'>-67841</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D200601_200831_StEqComps-CommonStock' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D200601_200831_StEqComps-AddPaidInCap' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D200601_200831_StEqComps-RetainedEarnings' unitRef='USD'>-9265</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D200601_200831' unitRef='USD'>-9265</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:SharesOutstanding decimals='INF' contextRef='I200831_StEqComps-CommonStock' unitRef='Shares'>7500000</us-gaap:SharesOutstanding>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200831_StEqComps-CommonStock' unitRef='USD'>7500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200831_StEqComps-AddPaidInCap' unitRef='USD'>67500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200831_StEqComps-RetainedEarnings' unitRef='USD'>-152106</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I200831' unitRef='USD'>-77106</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D200901_201130_StEqComps-CommonStock' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D200901_201130_StEqComps-AddPaidInCap' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D200901_201130_StEqComps-RetainedEarnings' unitRef='USD'>-6420</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D200901_201130' unitRef='USD'>-6420</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:SharesOutstanding decimals='INF' contextRef='I201130_StEqComps-CommonStock' unitRef='Shares'>7500000</us-gaap:SharesOutstanding>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I201130_StEqComps-CommonStock' unitRef='USD'>7500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I201130_StEqComps-AddPaidInCap' unitRef='USD'>67500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I201130_StEqComps-RetainedEarnings' unitRef='USD'>-158526</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I201130' unitRef='USD'>-83526</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:SharesOutstanding decimals='INF' contextRef='I190531_StEqComps-CommonStock' unitRef='Shares'>7500000</us-gaap:SharesOutstanding>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190531_StEqComps-CommonStock' unitRef='USD'>7500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190531_StEqComps-AddPaidInCap' unitRef='USD'>67500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190531_StEqComps-RetainedEarnings' unitRef='USD'>-104699</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190531' unitRef='USD'>-29699</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D190601_190831_StEqComps-CommonStock' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D190601_190831_StEqComps-AddPaidInCap' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D190601_190831_StEqComps-RetainedEarnings' unitRef='USD'>-6033</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D190601_190831' unitRef='USD'>-6033</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:SharesOutstanding decimals='INF' contextRef='I190831_StEqComps-CommonStock' unitRef='Shares'>7500000</us-gaap:SharesOutstanding>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190831_StEqComps-CommonStock' unitRef='USD'>7500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190831_StEqComps-AddPaidInCap' unitRef='USD'>67500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190831_StEqComps-RetainedEarnings' unitRef='USD'>-110732</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I190831' unitRef='USD'>-35732</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D190901_191130_StEqComps-CommonStock' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='128' contextRef='D190901_191130_StEqComps-AddPaidInCap' unitRef='USD'>0</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D190901_191130_StEqComps-RetainedEarnings' unitRef='USD'>-4910</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest decimals='INF' contextRef='D190901_191130' unitRef='USD'>-4910</us-gaap:NetIncomeLossIncludingPortionAttributableToNonredeemableNoncontrollingInterest>
	<us-gaap:SharesOutstanding decimals='INF' contextRef='I191130_StEqComps-CommonStock' unitRef='Shares'>7500000</us-gaap:SharesOutstanding>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I191130_StEqComps-CommonStock' unitRef='USD'>7500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I191130_StEqComps-AddPaidInCap' unitRef='USD'>67500</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I191130_StEqComps-RetainedEarnings' unitRef='USD'>-115642</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest decimals='INF' contextRef='I191130' unitRef='USD'>-40642</us-gaap:StockholdersEquityIncludingPortionAttributableToNoncontrollingInterest>
	<us-gaap:ProfitLoss decimals='INF' contextRef='D200601_201130' unitRef='USD'>-15685</us-gaap:ProfitLoss>
	<us-gaap:ProfitLoss decimals='INF' contextRef='D190601_191130' unitRef='USD'>-10943</us-gaap:ProfitLoss>
	<us-gaap:IncreaseDecreaseInAccountsPayableAndAccruedLiabilities decimals='INF' contextRef='D200601_201130' unitRef='USD'>15615</us-gaap:IncreaseDecreaseInAccountsPayableAndAccruedLiabilities>
	<us-gaap:IncreaseDecreaseInAccountsPayableAndAccruedLiabilities decimals='INF' contextRef='D190601_191130' unitRef='USD'>-500</us-gaap:IncreaseDecreaseInAccountsPayableAndAccruedLiabilities>
	<us-gaap:NetCashProvidedByUsedInOperatingActivities decimals='INF' contextRef='D200601_201130' unitRef='USD'>-70</us-gaap:NetCashProvidedByUsedInOperatingActivities>
	<us-gaap:NetCashProvidedByUsedInOperatingActivities decimals='INF' contextRef='D190601_191130' unitRef='USD'>-11443</us-gaap:NetCashProvidedByUsedInOperatingActivities>
	<us-gaap:CashAndCashEquivalentsPeriodIncreaseDecrease decimals='INF' contextRef='D200601_201130' unitRef='USD'>-70</us-gaap:CashAndCashEquivalentsPeriodIncreaseDecrease>
	<us-gaap:CashAndCashEquivalentsPeriodIncreaseDecrease decimals='INF' contextRef='D190601_191130' unitRef='USD'>-11443</us-gaap:CashAndCashEquivalentsPeriodIncreaseDecrease>
	<us-gaap:CashAndCashEquivalentsAtCarryingValue decimals='INF' contextRef='I200531' unitRef='USD'>1434</us-gaap:CashAndCashEquivalentsAtCarryingValue>
	<us-gaap:CashAndCashEquivalentsAtCarryingValue decimals='INF' contextRef='I190531' unitRef='USD'>12942</us-gaap:CashAndCashEquivalentsAtCarryingValue>
	<us-gaap:CashAndCashEquivalentsAtCarryingValue decimals='INF' contextRef='I201130' unitRef='USD'>1364</us-gaap:CashAndCashEquivalentsAtCarryingValue>
	<us-gaap:CashAndCashEquivalentsAtCarryingValue decimals='INF' contextRef='I191130' unitRef='USD'>1499</us-gaap:CashAndCashEquivalentsAtCarryingValue>
	<us-gaap:OtherSignificantNoncashTransactionValueOfConsiderationReceived1 decimals='INF' contextRef='D200601_201130' unitRef='USD'>12615</us-gaap:OtherSignificantNoncashTransactionValueOfConsiderationReceived1>
	<us-gaap:OtherSignificantNoncashTransactionValueOfConsiderationReceived1 decimals='128' contextRef='D190601_191130' unitRef='USD'>0</us-gaap:OtherSignificantNoncashTransactionValueOfConsiderationReceived1>
	<us-gaap:InterestPaid decimals='128' contextRef='D200601_201130' unitRef='USD'>0</us-gaap:InterestPaid>
	<us-gaap:InterestPaid decimals='128' contextRef='D190601_191130' unitRef='USD'>0</us-gaap:InterestPaid>
	<us-gaap:IncomeTaxesPaidNet decimals='128' contextRef='D200601_201130' unitRef='USD'>0</us-gaap:IncomeTaxesPaidNet>
	<us-gaap:IncomeTaxesPaidNet decimals='128' contextRef='D190601_191130' unitRef='USD'>0</us-gaap:IncomeTaxesPaidNet>
	<us-gaap:NatureOfOperations contextRef='D200601_201130'>&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt;margin-right:6.9pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;1.&lt;/kbd&gt;Nature of Operations and Continuance of Business&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;text-indent:-27pt;margin-left:27pt;margin-right:6.9pt&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt&apos;&gt;3am Technologies, Inc. (the &amp;#147;Company&amp;#148;) was incorporated in the state of Nevada on March 13, 2014. The Company has been in the exploration stage since its formation and has not commenced business operations. &lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt&apos;&gt;These financial statements have been prepared on a going concern basis, which implies the Company will continue to realize it assets and discharge its liabilities in the normal course of business. As of November 30, 2020, the Company has an accumulated deficit of $158,526. The continuation of the Company as a going concern is dependent upon the continued financial support from its shareholders, the ability of the Company to obtain necessary equity financing to continue operations, and the attainment of profitable operations. These factors raise substantial doubt regarding the Company&amp;#146;s ability to continue as a going concern. These financial statements do not include any adjustments to the recoverability and classification of recorded asset amounts and classification of liabilities that might be necessary should the Company be unable to continue as a going concern.&lt;/p&gt;</us-gaap:NatureOfOperations>
	<dei:EntityIncorporationStateCountryCode contextRef='D200601_201130'>NV</dei:EntityIncorporationStateCountryCode>
	<dei:EntityIncorporationDateOfIncorporation contextRef='D200601_201130'>2014-03-13</dei:EntityIncorporationDateOfIncorporation>
	<us-gaap:RetainedEarningsAccumulatedDeficit decimals='INF' contextRef='I201130' unitRef='USD'>-158526</us-gaap:RetainedEarningsAccumulatedDeficit>
	<us-gaap:SignificantAccountingPoliciesTextBlock contextRef='D200601_201130'>&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt;margin-right:6.9pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;2.&lt;/kbd&gt;Summary of Significant Accounting Policies&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;(a)&lt;/kbd&gt;Basis of Presentation&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;The accompanying unaudited interim financial statements of the Company have been prepared in accordance with accounting principles generally accepted in the United States of America and the rules of the Securities and Exchange Commission, and should be read in conjunction with the audited financial statements and notes thereto contained in the Company&amp;#146;s most recent Annual Financial Statements filed with the SEC on Form 10-K. In the opinion of management, all adjustments, consisting of normal recurring adjustments, necessary for a fair presentation of financial position and the results of operations for the interim period presented have been reflected herein. The results of operations for the interim period are not necessarily indicative of the results to be expected for the full year. Notes to the financial statements which would substantially duplicate the disclosures contained in the audited financial statements, as reported in the Form 10-K have been omitted. The Company has chosen May 31 as its year end.&lt;/p&gt;</us-gaap:SignificantAccountingPoliciesTextBlock>
	<us-gaap:BasisOfAccountingPolicyPolicyTextBlock contextRef='D200601_201130'>&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;(a)&lt;/kbd&gt;Basis of Presentation&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;The accompanying unaudited interim financial statements of the Company have been prepared in accordance with accounting principles generally accepted in the United States of America and the rules of the Securities and Exchange Commission, and should be read in conjunction with the audited financial statements and notes thereto contained in the Company&amp;#146;s most recent Annual Financial Statements filed with the SEC on Form 10-K. In the opinion of management, all adjustments, consisting of normal recurring adjustments, necessary for a fair presentation of financial position and the results of operations for the interim period presented have been reflected herein. The results of operations for the interim period are not necessarily indicative of the results to be expected for the full year. Notes to the financial statements which would substantially duplicate the disclosures contained in the audited financial statements, as reported in the Form 10-K have been omitted. The Company has chosen May 31 as its year end.&lt;/p&gt;</us-gaap:BasisOfAccountingPolicyPolicyTextBlock>
	<us-gaap:RelatedPartyTransactionsDisclosureTextBlock contextRef='D200601_201130'>&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt;margin-right:6.9pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;3.&lt;/kbd&gt;Related Party Transactions&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;(a)&lt;/kbd&gt;During the six months ended November 30, 2020, the President of the Company paid operating expense of $12,615 on behalf of the Company. As of November 30, 2020, and May 31, 2020, the Company was indebted to the President in the amount of $50,127 and $37,512, respectively. The loans are non-interest bearing, unsecured and due on demand.&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:18pt&apos;&gt;&amp;nbsp;&lt;/p&gt;&lt;p align=&quot;justify&quot; style=&apos;margin:0;margin-left:36pt&apos;&gt;&lt;kbd style=&apos;position:absolute;font:8pt Arial;margin-left:-18pt&apos;&gt;(b)&lt;/kbd&gt;The Company&amp;#146;s office space is provided by management at no cost.&amp;nbsp;&lt;/p&gt;</us-gaap:RelatedPartyTransactionsDisclosureTextBlock>
	<us-gaap:DebtInstrumentIssuer contextRef='D200601_201130'>President of the Company</us-gaap:DebtInstrumentIssuer>
	<us-gaap:ProceedsFromRelatedPartyDebt decimals='INF' contextRef='D200601_201130' unitRef='USD'>12615</us-gaap:ProceedsFromRelatedPartyDebt>
	<us-gaap:LongTermDebt decimals='INF' contextRef='I201130' unitRef='USD'>50127</us-gaap:LongTermDebt>
	<us-gaap:LongTermDebt decimals='INF' contextRef='I200531' unitRef='USD'>37512</us-gaap:LongTermDebt>
	<us-gaap:DebtInstrumentInterestRateStatedPercentage decimals='INF' contextRef='I201130' unitRef='Pure'>0.0000</us-gaap:DebtInstrumentInterestRateStatedPercentage>
	<us-gaap:DebtInstrumentCollateral contextRef='D200601_201130'>unsecured</us-gaap:DebtInstrumentCollateral>
	<us-gaap:DebtInstrumentPaymentTerms contextRef='D200601_201130'>due on demand</us-gaap:DebtInstrumentPaymentTerms>
	<us-gaap:PaymentsForRent decimals='INF' contextRef='D200601_201130' unitRef='USD'>0</us-gaap:PaymentsForRent>
	<context id='D200601_201130'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2020-06-01</startDate>
			<endDate>2020-11-30</endDate>
		</period>
	</context>
	<context id='I201130'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2020-11-30</instant>
		</period>
	</context>
	<context id='D201020'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2020-10-20</startDate>
			<endDate>2020-10-20</endDate>
		</period>
	</context>
	<context id='I200115'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2020-01-15</instant>
		</period>
	</context>
	<context id='I200531'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2020-05-31</instant>
		</period>
	</context>
	<context id='D200901_201130'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2020-09-01</startDate>
			<endDate>2020-11-30</endDate>
		</period>
	</context>
	<context id='D190901_191130'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2019-09-01</startDate>
			<endDate>2019-11-30</endDate>
		</period>
	</context>
	<context id='D190601_191130'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2019-06-01</startDate>
			<endDate>2019-11-30</endDate>
		</period>
	</context>
	<context id='I200531_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-05-31</instant>
		</period>
	</context>
	<context id='I200531_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-05-31</instant>
		</period>
	</context>
	<context id='I200531_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-05-31</instant>
		</period>
	</context>
	<context id='D200601_200831'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2020-06-01</startDate>
			<endDate>2020-08-31</endDate>
		</period>
	</context>
	<context id='D200601_200831_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2020-06-01</startDate>
			<endDate>2020-08-31</endDate>
		</period>
	</context>
	<context id='D200601_200831_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2020-06-01</startDate>
			<endDate>2020-08-31</endDate>
		</period>
	</context>
	<context id='D200601_200831_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2020-06-01</startDate>
			<endDate>2020-08-31</endDate>
		</period>
	</context>
	<context id='I200831'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2020-08-31</instant>
		</period>
	</context>
	<context id='I200831_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-08-31</instant>
		</period>
	</context>
	<context id='I200831_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-08-31</instant>
		</period>
	</context>
	<context id='I200831_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-08-31</instant>
		</period>
	</context>
	<context id='D200901_201130_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2020-09-01</startDate>
			<endDate>2020-11-30</endDate>
		</period>
	</context>
	<context id='D200901_201130_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2020-09-01</startDate>
			<endDate>2020-11-30</endDate>
		</period>
	</context>
	<context id='D200901_201130_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2020-09-01</startDate>
			<endDate>2020-11-30</endDate>
		</period>
	</context>
	<context id='I201130_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-11-30</instant>
		</period>
	</context>
	<context id='I201130_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-11-30</instant>
		</period>
	</context>
	<context id='I201130_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2020-11-30</instant>
		</period>
	</context>
	<context id='I190531'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2019-05-31</instant>
		</period>
	</context>
	<context id='I190531_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-05-31</instant>
		</period>
	</context>
	<context id='I190531_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-05-31</instant>
		</period>
	</context>
	<context id='I190531_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-05-31</instant>
		</period>
	</context>
	<context id='D190601_190831'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<startDate>2019-06-01</startDate>
			<endDate>2019-08-31</endDate>
		</period>
	</context>
	<context id='D190601_190831_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2019-06-01</startDate>
			<endDate>2019-08-31</endDate>
		</period>
	</context>
	<context id='D190601_190831_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2019-06-01</startDate>
			<endDate>2019-08-31</endDate>
		</period>
	</context>
	<context id='D190601_190831_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2019-06-01</startDate>
			<endDate>2019-08-31</endDate>
		</period>
	</context>
	<context id='I190831'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2019-08-31</instant>
		</period>
	</context>
	<context id='I190831_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-08-31</instant>
		</period>
	</context>
	<context id='I190831_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-08-31</instant>
		</period>
	</context>
	<context id='I190831_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-08-31</instant>
		</period>
	</context>
	<context id='D190901_191130_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2019-09-01</startDate>
			<endDate>2019-11-30</endDate>
		</period>
	</context>
	<context id='D190901_191130_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2019-09-01</startDate>
			<endDate>2019-11-30</endDate>
		</period>
	</context>
	<context id='D190901_191130_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<startDate>2019-09-01</startDate>
			<endDate>2019-11-30</endDate>
		</period>
	</context>
	<context id='I191130'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
		</entity>
		<period>
			<instant>2019-11-30</instant>
		</period>
	</context>
	<context id='I191130_StEqComps-CommonStock'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:CommonStockMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-11-30</instant>
		</period>
	</context>
	<context id='I191130_StEqComps-AddPaidInCap'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:AdditionalPaidInCapitalMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-11-30</instant>
		</period>
	</context>
	<context id='I191130_StEqComps-RetainedEarnings'>
		<entity>
			<identifier scheme='http://www.sec.gov/CIK'>0001667615</identifier>
			<segment><xbrldi:explicitMember dimension='us-gaap:StatementEquityComponentsAxis'>us-gaap:RetainedEarningsMember</xbrldi:explicitMember></segment>
		</entity>
		<period>
			<instant>2019-11-30</instant>
		</period>
	</context>
	<unit id='Pure'>
		<measure>xbrli:pure</measure>
	</unit>
	<unit id='USD'>
		<measure>iso4217:USD</measure>
	</unit>
	<unit id='Shares'>
		<measure>xbrli:shares</measure>
	</unit>
	<unit id='UsdPerShare'>
		<divide>
			<unitNumerator>
				<measure>iso4217:USD</measure>
			</unitNumerator>
			<unitDenominator>
				<measure>xbrli:shares</measure>
			</unitDenominator>
		</divide>
	</unit>
</xbrl>
