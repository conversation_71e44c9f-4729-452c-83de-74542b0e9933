[project]
name = "secdaily"
version = "0.2.2"
authors = [{ name = "<PERSON>joerg", email = "<EMAIL>" }]
description = "A tool that replicates the quarterly Financial Statement Datasets from the SEC (https://www.sec.gov/dera/data/financial-statement-data-sets), but on a daily basis."
readme = "README.md"
requires-python = ">=3.10"
license = "Apache-2.0"

maintainers = [
    { name = "<PERSON><PERSON><PERSON><PERSON> Wingeier", email = "<EMAIL>" },
]

classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Topic :: Office/Business :: Financial",
    "Topic :: Office/Business :: Financial :: Investment",
    "Intended Audience :: Developers",
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Utilities",
    "Natural Language :: English",
]

# Your existing keywords are good, maybe add these:
keywords = [
    "SEC.GOV",
    "SEC EDGAR",
    "SEC Filing",
    "EDGAR",
    "Finance",
    "CIK",
    "10-Q",
    "10-K",
    "Financial Statements",
    "Financial Statements Dataset",
    "Financial Analysis",
    "Data Processing",
    "Financial Data",
    "SEC API",
    "XBRL",
]

dependencies = [
    "pandas>=2.2.0",
    "requests>=2.32.0",
    "pathos>=0.3.0",
    "lxml>=4.8.0",
    "pyarrow>=19.0.0",
]

[project.urls]
"Homepage" = "https://hansjoergw.github.io/sec-financial-statement-data-set-daily-processing/"
"Bug Tracker" = "https://github.com/HansjoergW/sec-financial-statement-data-set-daily-processing/issues"
"Github" = "https://github.com/HansjoergW/sec-financial-statement-data-set-daily-processing"
"Funding" = "https://github.com/sponsors/HansjoergW"
"Forum" = "https://github.com/HansjoergW/sec-financial-statement-data-set-daily-processing/discussions"
"Change Log" = "https://github.com/HansjoergW/sec-financial-statement-data-set-daily-processing/blob/main/CHANGELOG.md"


[project.optional-dependencies]
dev = ["pytest", "pylint", "black", "ruff"]

[tool.poetry]
packages = [{ include = "secdaily", from = "src" }]

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.2"
pylint = "^3.0.3"
black = "^24.2.0"
ruff = "^0.5.3"

[tool.ruff]
# Explicitly enable F401 (unused imports) and other rules
lint.select = [
    "F401", # unused imports
    "E",    # pycodestyle errors
    "F",    # other pyflakes rules
    "I",    # isort
    "W",    # pycodestyle warnings
]
line-length = 120
fix = true
src = ["src", "tests"]

[tool.black]
line-length = 120

[tool.pylint]
ignore-paths = ["sandbox", "notes"]

[tool.pylint.'MAIN']
# Base settings that apply to everything
disable = [
    "C0111", # missing-docstring
    "C0103", # invalid-name
    "C0114", # missing-module-docstring
    "C0115", # missing-class-docstring
    "C0116", # missing-function-docstring
    "R0801", # duplicate-code
    "R0902", # too-many-instance-attributes
    "R0903", # too-few-public-methods
    "R0912", # too-many-branches
    "R0913", # too-many-arguments
    "R0914", # too-many-locals
    "R0915", # too-many-statements
    "R0917", # too-many-positional-arguments
    "W0511", # fixme
]

[tool.pylint.'tests/*']
# More relaxed settings for test files
disable = [
    "R0801", # duplicate-code
    "W0212", # protected-access
    "W0612", # unused-variable (common in pytest with fixtures)
    "W0613", # unused-argument (common in pytest with fixtures)
    "W0621", # redefined-outer-name (common in pytest with fixtures)
]

extension-pkg-allow-list = ["lxml", "numpy"]

[tool.pylint.format]
max-line-length = 120

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
testpaths = [
    "tests",
    "sandbox/testintegration", # Integration Tests
    "sandbox/trials",          # Experimental Code
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-ra -q --strict-markers"
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
]
