copilot:
 - Möglichkeit für System Prompts sollte vorhanden sein
 - sehr langsam, bis grössere Klassen/files upgedated werden, oder auch 
   um generierten Code zu "mergen"/akzeptieren.
   Claude 3.5 Modell scheint etwas schneller zu sein


augment code:
 - keep an eye on the augmentcode.com subscription:
   does it really switch to community after the 15.4 ?
 - ein<PERSON>ch zu installieren, hat schnell verstanden, worum es im code geht.
   scheint auch key Konzepte erkannt zu haben, z.B. Parallelisierung.
   Scheint dafür aber in erster Linie code_overview.md gelesen zu haben.


ruff:
 - linting check with ruff
  poetry run ruff check --fix .
  poetry run ruff check --select W291 --fix --unsafe-fixes .   # spezifisches Problem fixen
  poetry run ruff check --statistics ./src # statistics
          256     E501    [ ] line-too-long
            4     E712    [*] true-false-comparison
            4     F841    [*] unused-variable
            3     E711    [*] none-comparison
            2     E722    [ ] bare-except
            1     E731    [*] lambda-assignment  
  
  poetry run ruff check --output-format concise ./src # concise output
