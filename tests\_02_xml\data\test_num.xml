<?xml version="1.0" encoding="utf-8"?>
<xbrl
        xml:lang="en-US"
        xmlns="http://www.xbrl.org/2003/instance"
        xmlns:aapl="http://www.apple.com/20200926"
        xmlns:country="http://xbrl.sec.gov/country/2020-01-31"
        xmlns:dei="http://xbrl.sec.gov/dei/2020-01-31"
        xmlns:iso4217="http://www.xbrl.org/2003/iso4217"
        xmlns:link="http://www.xbrl.org/2003/linkbase"
        xmlns:srt="http://fasb.org/srt/2020-01-31"
        xmlns:us-gaap="http://fasb.org/us-gaap/2020-01-31"
        xmlns:xbrldi="http://xbrl.org/2006/xbrldi"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <link:schemaRef xlink:href="aapl-20200926.xsd" xlink:type="simple"/>
    <context id="i223bd574caab4f739f73936be6065c72_D20190929-20200926">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0000320193</identifier>
        </entity>
        <period>
            <startDate>2019-09-29</startDate>
            <endDate>2020-09-26</endDate>
        </period>
    </context>
    <context id="i9f9a068454cf45bc8dabe3edf6e6b899_D20190929-20200926">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0000320193</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">us-gaap:CommonStockMember</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-09-29</startDate>
            <endDate>2020-09-26</endDate>
        </period>
    </context>
    <context id="ic9b02aa81ea048e3b2d21ff1ff5ee96d_D20190929-20200926">
        <entity>
            <identifier scheme="http://www.sec.gov/CIK">0000320193</identifier>
            <segment>
                <xbrldi:explicitMember dimension="us-gaap:StatementClassOfStockAxis">aapl:A1.000NotesDue2022Member</xbrldi:explicitMember>
            </segment>
        </entity>
        <period>
            <startDate>2019-09-29</startDate>
            <endDate>2020-09-26</endDate>
        </period>
    </context>
    <us-gaap:ResearchAndDevelopmentExpense
            contextRef="i223bd574caab4f739f73936be6065c72_D20190929-20200926"
            decimals="-6"
            id="id3VybDovL2RvY3MudjEvZG9jOmVmNzgxYWI1OGU0ZjRmY2FhODcyZGRiZDMwZGE0MGUxL3NlYzplZjc4MWFiNThlNGY0ZmNhYTg3MmRkYmQzMGRhNDBlMV84NS9mcmFnOjlhZmQ0ZDY0MDMwNjRjMmE5NDAxMWI4ZjFjMmE2Zjc1L3RhYmxlOjZjMTBjOWQ2ZjgzMTRhMGVhYjQ4NzAxNjQ2OTRkOWE2L3RhYmxlcmFuZ2U6NmMxMGM5ZDZmODMxNGEwZWFiNDg3MDE2NDY5NGQ5YTZfMTQtMS0xLTEtMA_4dc5e576-b829-4c9d-aa17-2472a6530aaa"
            unitRef="usd">18752000000</us-gaap:ResearchAndDevelopmentExpense>
    <us-gaap:SellingGeneralAndAdministrativeExpense
            contextRef="i223bd574caab4f739f73936be6065c72_D20190929-20200926"
            decimals="-6"
            id="id3VybDovL2RvY3MudjEvZG9jOmVmNzgxYWI1OGU0ZjRmY2FhODcyZGRiZDMwZGE0MGUxL3NlYzplZjc4MWFiNThlNGY0ZmNhYTg3MmRkYmQzMGRhNDBlMV84NS9mcmFnOjlhZmQ0ZDY0MDMwNjRjMmE5NDAxMWI4ZjFjMmE2Zjc1L3RhYmxlOjZjMTBjOWQ2ZjgzMTRhMGVhYjQ4NzAxNjQ2OTRkOWE2L3RhYmxlcmFuZ2U6NmMxMGM5ZDZmODMxNGEwZWFiNDg3MDE2NDY5NGQ5YTZfMTUtMS0xLTEtMA_d280f5de-ad11-460b-8898-ed13acbfb1a0"
            unitRef="usd">19916000000</us-gaap:SellingGeneralAndAdministrativeExpense>
    <us-gaap:NonoperatingIncomeExpense
            contextRef="i223bd574caab4f739f73936be6065c72_D20190929-20200926"
            decimals="-6"
            id="id3VybDovL2RvY3MudjEvZG9jOmVmNzgxYWI1OGU0ZjRmY2FhODcyZGRiZDMwZGE0MGUxL3NlYzplZjc4MWFiNThlNGY0ZmNhYTg3MmRkYmQzMGRhNDBlMV84NS9mcmFnOjlhZmQ0ZDY0MDMwNjRjMmE5NDAxMWI4ZjFjMmE2Zjc1L3RhYmxlOjZjMTBjOWQ2ZjgzMTRhMGVhYjQ4NzAxNjQ2OTRkOWE2L3RhYmxlcmFuZ2U6NmMxMGM5ZDZmODMxNGEwZWFiNDg3MDE2NDY5NGQ5YTZfMTktMS0xLTEtMA_c50c169e-f577-41b3-a704-fcfe7505b0eb"
            unitRef="usd">803000000</us-gaap:NonoperatingIncomeExpense>
</xbrl>