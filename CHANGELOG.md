# Changelog

## ... 0.2.0 -> 0.2.1
Fixes:
 - order of parameters in logging statement inside housekeeping
 - fix mixed up pre and num file name during sec-style formatting
 - removed helper column fy_real from final sub_df


## 2025-05-16 0.1.0 -> 0.2.0
New: 
 - \_\_version\_\_ constant in code
 - prints an advisory message, if a newer version is available
 - build quarter zip files from daily zip files
 - clean up logic that removes "old" data
   - intermediary files
   - database entries
   - quarter and daily zip files  
 - Configuration is encapsulated in a configuration class

API changes:
 - Configuration is encapsulated in a configuration class.
   Please have a look at the README for more information.


## 2025-04-18 0.0.1 -> 0.1.0
- readme updated
- end-to-end tests for the whole parsing process added
- spnsoring message added


## 2025-04-15 0.0.1
Initial release
Note: documentation is not yet complete